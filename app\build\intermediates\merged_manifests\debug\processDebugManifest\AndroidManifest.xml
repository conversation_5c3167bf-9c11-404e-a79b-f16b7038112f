<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="cn.ykload.flowmix"
    android:versionCode="1"
    android:versionName="Alpha" >

    <uses-sdk
        android:minSdkVersion="28"
        android:targetSdkVersion="36" />

    <!-- 音频效果权限 -->
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />

    <!-- 蓝牙权限 - 用于检测蓝牙音频设备 -->
    <uses-permission
        android:name="android.permission.BLUETOOTH"
        android:maxSdkVersion="30" />
    <uses-permission
        android:name="android.permission.BLUETOOTH_ADMIN"
        android:maxSdkVersion="30" />

    <!-- Android 12+ 蓝牙权限 -->
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission
        android:name="android.permission.BLUETOOTH_SCAN"
        android:usesPermissionFlags="neverForLocation" />

    <!-- 网络权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <!-- 文件读取权限 - Android 12及以下 -->
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />

    <!-- Android 13+ 媒体文件权限 -->
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />

    <!-- 管理外部存储权限 - 用于访问所有文件 -->
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />

    <!-- 查询其他应用的权限 -->
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />

    <!-- 前台服务权限 - 用于保活服务 -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />

    <!-- 通知权限 - Android 13+ -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <!-- 开机自启动权限 -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />

    <!-- 唤醒锁权限 - 用于保持CPU运行 -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <!-- 声明需要查询的特定应用 -->
    <queries>
        <package android:name="cn.ykload.seeq" />
    </queries>

    <permission
        android:name="cn.ykload.flowmix.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="cn.ykload.flowmix.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:debuggable="true"
        android:extractNativeLibs="false"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:testOnly="true"
        android:theme="@style/Theme.Flowmix" >

        <!-- 应用级元数据：声明为音频效果应用 -->
        <meta-data
            android:name="android.media.audio_effect_app"
            android:value="true" />
        <meta-data
            android:name="android.media.audio_effect.equalizer"
            android:value="Flowmix" />
        <meta-data
            android:name="android.media.audio_effect.description"
            android:value="Flowmix - 全局 AutoEq 应用  \awa/" />

        <activity
            android:name="cn.ykload.flowmix.MainActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:launchMode="singleTop"
            android:theme="@style/Theme.Flowmix" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <!-- 处理通知点击事件 -->
            <intent-filter>
                <action android:name="cn.ykload.flowmix.notification.NotificationHelper.ACTION_TOGGLE_FLOWMIX" />
                <action android:name="cn.ykload.flowmix.notification.NotificationHelper.ACTION_OPEN_APP" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <!-- 均衡器Activity - 处理来自其他应用的音频效果控制请求 -->
        <activity
            android:name="cn.ykload.flowmix.EqualizerActivity"
            android:exported="true"
            android:label="Flowmix - 全局 AutoEq 应用  \awa/"
            android:launchMode="singleTop"
            android:theme="@style/Theme.Flowmix" >

            <!-- 音频效果控制会话意图过滤器 - 注册为系统均衡器 -->
            <intent-filter>
                <action android:name="android.media.action.OPEN_AUDIO_EFFECT_CONTROL_SESSION" />
                <action android:name="android.media.action.CLOSE_AUDIO_EFFECT_CONTROL_SESSION" />
                <action android:name="android.media.action.DISPLAY_AUDIO_EFFECT_CONTROL_PANEL" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <!-- 元数据：标识为音频效果应用 -->
            <meta-data
                android:name="android.media.audio_effect"
                android:value="true" />
            <meta-data
                android:name="android.media.audio_effect.equalizer"
                android:value="true" />
        </activity>

        <!-- Flowmix 保活前台服务 -->
        <service
            android:name="cn.ykload.flowmix.service.FlowmixKeepAliveService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="mediaPlayback"
            android:stopWithTask="false" />

        <!-- 开机自启动接收器 -->
        <receiver
            android:name="cn.ykload.flowmix.receiver.BootReceiver"
            android:enabled="true"
            android:exported="true" >
            <intent-filter android:priority="1000" >
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
                <action android:name="android.intent.action.PACKAGE_REPLACED" />

                <data android:scheme="package" />
            </intent-filter>
        </receiver>

        <activity
            android:name="androidx.compose.ui.tooling.PreviewActivity"
            android:exported="true" />

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="cn.ykload.flowmix.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <activity
            android:name="androidx.activity.ComponentActivity"
            android:exported="true"
            android:theme="@android:style/Theme.Material.Light.NoActionBar" />

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>
    </application>

</manifest>